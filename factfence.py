from dataclasses import asdict

import pandas as pd
import streamlit as st

from pipeline.pipeline import FactFencePipeline
from config.config_service import ConfigurationService
from utils.testdata_loader import load_testdata_yaml
from utils.testdata_types import (
    TestData, TestResult,
    StrategyTestResult, TestResults
)
from config.config_types import AppConfig, FactExtractionConfig, PromptStrategy


def start():
    st.title("FactFence - Fact Verification")
    config = ConfigurationService().setup()
    pipeline = FactFencePipeline(config)

    # st.header("Input")
    # claim = st.text_area("Enter a claim to verify:", height=300)
    # verify_button = st.button("Verify")
    #
    # loader_placeholder = st.empty()
    # results_placeholder = st.container()
    #
    # if verify_button:
    #     if claim:
    #         with loader_placeholder.container():
    #             progress_bar = st.progress(0)
    #             status_text = st.empty()
    #
    #             status_text.text("Extracting facts...")
    #             progress_bar.progress(10)
    #
    #             results = process_with_progress(pipeline, claim, progress_bar, status_text)
    #
    #             loader_placeholder.empty()
    #
    #         with results_placeholder:
    #             st.header("Results")
    #             for result in results:
    #                 # Create tabs for better organization of all the data
    #                 summary_tab, facts_tab, retrieval_tab, details_tab = st.tabs([
    #                     "Summary", "Extracted Facts", "Knowledge Retrieval", "Verification Details"
    #                 ])
    #
    #                 # Summary tab - Shows the main verification result
    #                 with summary_tab:
    #                     status = result.verification.status
    #
    #                     # Display the verification status with appropriate styling
    #                     if status == "SUPPORTS":
    #                         st.success(f"Claim: {result.original_claim}")
    #                         st.success(f"Status: {status}")
    #                         st.success(f"Explanation: {result.verification.explanation}")
    #                     elif status == "REFUTES":
    #                         st.error(f"Claim: {result.original_claim}")
    #                         st.error(f"Status: {status}")
    #                         st.error(f"Explanation: {result.verification.explanation}")
    #                     else:
    #                         st.warning(f"Claim: {result.original_claim}")
    #                         st.warning(f"Status: {status}")
    #                         st.warning(f"Explanation: {result.verification.explanation}")
    #
    #                 # Facts tab - Show all extracted facts
    #                 with facts_tab:
    #                     # Show prompt details
    #                     with st.expander("Extraction Prompts"):
    #                         if hasattr(result.extraction, 'extraction_prompt_request') and result.extraction.extraction_prompt_request:
    #                             st.subheader("Extraction Prompt Request")
    #                             st.text(result.extraction.extraction_prompt_request)
    #
    #                         if hasattr(result.extraction, 'extraction_prompt_result') and result.extraction.extraction_prompt_result:
    #                             st.subheader("Extraction Prompt Result")
    #                             st.text(result.extraction.extraction_prompt_result)
    #
    #
    #
    #                     # Raw extraction (already in your code)
    #                     if hasattr(result.extraction, 'raw_extraction') and result.extraction.raw_extraction:
    #                         st.subheader("Raw LLM Extraction")
    #                         st.text(result.extraction.raw_extraction)
    #
    #                     # Structured facts (enhancing what's already in your code)
    #                     st.subheader("Structured Facts")
    #                     if result.extraction.facts:
    #                         # Create a table for better visualization
    #                         facts_data = {
    #                             "Subject": [],
    #                             "Predicate": [],
    #                             "Object": []
    #                         }
    #
    #                         for fact in result.extraction.facts:
    #                             facts_data["Subject"].append(fact.subject)
    #                             facts_data["Predicate"].append(fact.predicate)
    #                             facts_data["Object"].append(fact.object)
    #
    #                         st.dataframe(pd.DataFrame(facts_data))
    #
    #                         # Show individual fact details
    #                         for i, fact in enumerate(result.extraction.facts):
    #                             with st.expander(f"Fact {i+1}: {fact.subject} - {fact.predicate} - {fact.object}"):
    #                                 # Display any additional attributes of the fact
    #                                 for attr, value in fact.__dict__.items():
    #                                     if attr not in ('subject', 'predicate', 'object'):
    #                                         st.write(f"{attr}: {value}")
    #                     else:
    #                         st.info("No facts were extracted from this claim.")
    #
    #
    #
    #
    #                                 else:
    #                                     st.info("No connections")
    #
    #                     # Show paths
    #                     if hasattr(result.extraction, 'paths') and result.extraction.paths:
    #                         st.subheader("Knowledge Graph Paths")
    #                         for i, path in enumerate(result.extraction.paths):
    #                             with st.expander(f"Path {i+1}"):
    #                                 # Display entities and relations in an alternating pattern
    #                                 if path.entities:
    #                                     path_text = path.entities[0]
    #                                     for j in range(len(path.relations)):
    #                                         if j < len(path.relations):
    #                                             path_text += f" --[{path.relations[j]}]--> "
    #                                         if j+1 < len(path.entities):
    #                                             path_text += path.entities[j+1]
    #                                     st.write(path_text)
    #                                 else:
    #                                     st.info("Empty path")
    #
    #                 # Retrieval tab - Show knowledge retrieval details
    #                 with retrieval_tab:
    #                     if result.retrieval.retrievals:
    #                         for i, retrieval in enumerate(result.retrieval.retrievals):
    #                             # Create a summary for the expander title
    #                             fact_summary = f"{retrieval.fact.subject} - {retrieval.fact.predicate} - {retrieval.fact.object}"
    #                             knowledge_count = len(retrieval.knowledge)
    #
    #                             with st.expander(f"Retrieval {i+1}: {fact_summary} ({knowledge_count} matches)"):
    #                                 # Display the query
    #                                 st.subheader("Query")
    #                                 st.write(f"Subject: {retrieval.fact.subject}")
    #                                 st.write(f"Predicate: {retrieval.fact.predicate}")
    #                                 st.write(f"Object: {retrieval.fact.object}")
    #
    #                                 # Display overall match details
    #                                 st.subheader("Overall Match Details")
    #                                 confidence = retrieval.calculate_confidence()
    #                                 st.write(f"Average Confidence: {confidence:.2f}")
    #                                 st.progress(min(float(confidence), 1.0))
    #                                 st.write(f"Number of Knowledge Matches: {knowledge_count}")
    #
    #                                 # Display each retrieved knowledge match
    #                                 st.subheader("Retrieved Knowledge Matches")
    #                                 for j, fact_knowledge in enumerate(retrieval.knowledge):
    #                                     with st.expander(f"Match {j+1}"):
    #                                         col1, col2, col3 = st.columns(3)
    #
    #                                         with col1:
    #                                             st.write("**Subject:**")
    #                                             if fact_knowledge.subject:
    #                                                 st.write(f"Name: {fact_knowledge.subject.name}")
    #                                                 st.write(f"ID: {fact_knowledge.subject.id}")
    #                                                 st.write(f"Similarity: {fact_knowledge.subject.similarity:.2f}")
    #                                             else:
    #                                                 st.write("Not found")
    #
    #                                         with col2:
    #                                             st.write("**Predicate:**")
    #                                             if fact_knowledge.predicate:
    #                                                 st.write(f"Type: {fact_knowledge.predicate.type}")
    #                                                 st.write(f"Similarity: {fact_knowledge.predicate.similarity:.2f}")
    #                                             else:
    #                                                 st.write("Not found")
    #
    #                                         with col3:
    #                                             st.write("**Object:**")
    #                                             if fact_knowledge.object:
    #                                                 st.write(f"Name: {fact_knowledge.object.name}")
    #                                                 st.write(f"ID: {fact_knowledge.object.id}")
    #                                                 st.write(f"Similarity: {fact_knowledge.object.similarity:.2f}")
    #                                             else:
    #                                                 st.write("Not found")
    #
    #                                         # Calculate individual match confidence
    #                                         individual_scores = []
    #                                         if fact_knowledge.subject:
    #                                             individual_scores.append(fact_knowledge.subject.similarity)
    #                                         if fact_knowledge.predicate:
    #                                             individual_scores.append(fact_knowledge.predicate.similarity)
    #                                         if fact_knowledge.object:
    #                                             individual_scores.append(fact_knowledge.object.similarity)
    #
    #                                         if individual_scores:
    #                                             individual_confidence = sum(individual_scores) / len(individual_scores)
    #                                             st.write(f"**Match Confidence: {individual_confidence:.2f}**")
    #                                             st.progress(min(float(individual_confidence), 1.0))
    #                     else:
    #                         st.info("No knowledge retrieval results available.")
    #
    #                 # Details tab - Additional verification details
    #                 with details_tab:
    #                     st.subheader("Verification Process Details")
    #
    #                     # Display all verification data
    #                     st.write(f"Original Claim: {result.verification.original_claim}")
    #                     st.write(f"Status: {result.verification.status}")
    #                     st.write(f"Explanation: {result.verification.explanation}")
    #
    #                     # Display reasoning steps if available
    #                     if hasattr(result.verification, 'reasoning_steps') and result.verification.reasoning_steps:
    #                         st.subheader("Reasoning Steps")
    #                         for i, step in enumerate(result.verification.reasoning_steps):
    #                             st.markdown(f"**Step {i+1}:** {step}")
    #
    #                     # Display verification prompt if available
    #                     if hasattr(result.verification, 'verification_prompt') and result.verification.verification_prompt:
    #                         with st.expander("Verification Prompt"):
    #                             st.text(result.verification.verification_prompt)
    #
    #                     # Display raw LLM response if available
    #                     if hasattr(result.verification, 'raw_verification') and result.verification.raw_verification:
    #                         with st.expander("Raw Verification Response"):
    #                             st.text(result.verification.raw_verification)
    #
    #                     # Display confidence score if available
    #                     if hasattr(result.verification, 'confidence') and result.verification.confidence:
    #                         st.subheader("Confidence Score")
    #                         st.write(f"Confidence: {result.verification.confidence:.2f}")
    #                         st.progress(min(float(result.verification.confidence), 1.0))
    #
    #                     # Display model used if available
    #                     if hasattr(result.verification, 'model_used') and result.verification.model_used:
    #                         st.write(f"Model Used: {result.verification.model_used}")
    #
    #                     # Display verification metadata
    #                     if hasattr(result.verification, 'metadata') and result.verification.metadata:
    #                         st.subheader("Verification Metadata")
    #                         for key, value in result.verification.metadata.items():
    #                             st.write(f"**{key}:** {value}")
    #
    #                     # Display all other attributes not explicitly handled
    #                     st.subheader("All Verification Attributes")
    #                     with st.expander("Show all attributes"):
    #                         for attr, value in result.verification.__dict__.items():
    #                             if attr not in ['original_claim', 'status', 'explanation', 'reasoning_steps',
    #                                            'verification_prompt', 'raw_verification', 'confidence',
    #                                            'model_used', 'metadata']:
    #                                 st.write(f"**{attr}:** {value}")

    # Multi-Strategy Testing section
    st.header("FactFence Multi-Strategy Testing")
    test_button = st.button("Run all Tests")
    test_placeholder = st.empty()

    if test_button:
        with test_placeholder.container():
            # Show loader for testing
            test_progress = st.progress(0)
            test_status = st.empty()
            test_status.text("Starting multi-strategy testing...")

            # Run multi-strategy evaluation with progress updates
            multi_results = run_multi_strategy_evaluation_with_progress(config, test_progress, test_status)

            if multi_results.strategy_results:
                # Display results
                st.subheader("Test Results Summary")

                # Create summary table
                summary_data = []
                for strategy_result in multi_results.strategy_results:
                    total_tests = len(strategy_result.test_results)
                    correct_tests = sum(1 for r in strategy_result.test_results if r.correct)

                    summary_data.append({
                        "Strategy": strategy_result.strategy_name,
                        "Total Tests": total_tests,
                        "Correct": correct_tests,
                    })

                summary_df = pd.DataFrame(summary_data)
                st.table(summary_df)

                # Create detailed results table
                st.subheader("Detailed Test Results")

                # Prepare data for detailed table
                detailed_data = []
                for strategy_result in multi_results.strategy_results:
                    for test_result in strategy_result.test_results:
                        detailed_data.append({
                            "Strategy": strategy_result.strategy_name,
                            "Test ID": test_result.test_case_id,
                            "Claim": test_result.claim[:100] + "..." if len(test_result.claim) > 100 else test_result.claim,
                            "Expected": test_result.expected_status,
                            "Actual": test_result.actual_status,
                            "Correct": "✅" if test_result.correct else "❌"
                        })

                detailed_df = pd.DataFrame(detailed_data)
                st.dataframe(detailed_df, use_container_width=True)

                # Strategy-specific detailed results
                st.subheader("Strategy-Specific Results")

                for strategy_result in multi_results.strategy_results:
                    with st.expander(f"Strategy: {strategy_result.strategy_name}"):
                        st.write("**Individual Test Results:**")

                        for test_result in strategy_result.test_results:
                            status_icon = "✅" if test_result.correct else "❌"

                            with st.expander(f"{status_icon} {test_result.test_case_id}: {test_result.claim[:50]}..."):
                                col1, col2 = st.columns(2)

                                with col1:
                                    st.write(f"**Test ID:** {test_result.test_case_id}")
                                    st.write(f"**Expected:** {test_result.expected_status}")
                                    st.write(f"**Actual:** {test_result.actual_status}")
                                    st.write(f"**Correct:** {'Yes' if test_result.correct else 'No'}")

                                with col2:
                                    st.write(f"**Claim:**")
                                    st.write(test_result.claim)

                                st.write(f"**Explanation:**")
                                st.write(test_result.explanation)

                # Overall statistics
                st.subheader("Overall Statistics")

                total_tests_all = sum(len(sr.test_results) for sr in multi_results.strategy_results)
                total_correct_all = sum(sum(1 for r in sr.test_results if r.correct) for sr in multi_results.strategy_results)

                col1, col2 = st.columns(3)
                with col1:
                    st.metric("Total Tests", total_tests_all)
                with col2:
                    st.metric("Total Correct", total_correct_all)

                # Export results
                st.subheader("Export Results")
                if st.button("Download Results as JSON"):
                    import json
                    results_dict = asdict(multi_results)
                    json_str = json.dumps(results_dict, indent=2, ensure_ascii=False)
                    st.download_button(
                        label="Download JSON",
                        data=json_str,
                        file_name="multi_strategy_test_results.json",
                        mime="application/json"
                    )
            else:
                st.error("No test results available. Please check your configuration and test data.")


# def process_with_progress(pipeline, claim, progress_bar, status_text):
#     """
#     Process a claim through the pipeline with progress updates.
#
#     Args:
#         pipeline: The FactFencePipeline instance
#         claim: The claim to verify
#         progress_bar: Streamlit progress bar
#         status_text: Streamlit text element for status updates
#
#     Returns:
#         Pipeline results
#     """
#     # Extract facts (30% of progress)
#     status_text.text("Extracting facts...")
#     extraction_result = pipeline.extractor.extract_facts(claim)
#     progress_bar.progress(30)
#
#     # Retrieve knowledge (30% -> 60%)
#     status_text.text("Retrieving knowledge...")
#     retrieval_results = pipeline.retriever.retrieve(extraction_result)
#     progress_bar.progress(60)
#
#     # Verify facts (60% -> 90%)
#     status_text.text("Verifying facts...")
#     verification_results = pipeline.verifier.verify(retrieval_results)
#     progress_bar.progress(90)
#
#     # Finalize (90% -> 100%)
#     status_text.text("Finalizing results...")
#     results = [Result(
#         original_claim=claim,
#         extraction=extraction_result,
#         retrieval=retrieval_results,
#         verification=verification_results
#     )]
#     progress_bar.progress(100)
#     status_text.text("Verification complete!")
#
#     # Small delay to show the completion message
#     time.sleep(0.5)
#
#     return results


def run_pipeline_for_strategy_ui(config: AppConfig, strategy: PromptStrategy, test_data: TestData, status_text) -> StrategyTestResult:
    status_text.text(f"Running pipeline for strategy: {strategy}")

    # Create a modified config with single strategy
    config.fact_extraction.strategy = strategy

    pipeline = FactFencePipeline(config)
    test_results = pipeline.verify_claims(test_data.test_cases)
    strategy_results = StrategyTestResult(
        strategy_name=strategy,
        test_results=test_results
    )

    return strategy_results


def run_multi_strategy_evaluation_with_progress(config: AppConfig, progress_bar, status_text) -> TestResults:
    # Load test data from YAML
    status_text.text(f"Loading test data from: {config.input.testdata_file}")
    try:
        test_data = load_testdata_yaml(config.input.testdata_file)
        status_text.text(f"Loaded {len(test_data.test_cases)} test cases")
    except Exception as e:
        st.error(f"Failed to load test data: {str(e)}")
        return TestResults()

    # Get strategies to run
    strategies = config.fact_extraction.strategies
    status_text.text(f"Running strategies: {', '.join(strategies)}")

    # Run pipeline for each strategy
    multi_strategy_results = TestResults()

    for i, strategy in enumerate(strategies):
        try:
            # Update progress for strategy
            strategy_progress_start = i / len(strategies)
            strategy_progress_end = (i + 1) / len(strategies)

            # Create a sub-progress bar for this strategy
            strategy_results = run_pipeline_for_strategy_ui(
                config, strategy, test_data, status_text
            )
            multi_strategy_results.strategy_results.append(strategy_results)

            # Update overall progress
            progress_bar.progress(strategy_progress_end)

        except Exception as e:
            st.error(f"Failed to run strategy {strategy}: {str(e)}")
            continue

    status_text.text("Evaluation complete!")
    return multi_strategy_results


# Start the application
start()

