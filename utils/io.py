"""
IO Utilities - Read/write helpers

This module provides utilities for reading and writing data in various formats.
"""

import os
import json
import logging
from typing import Dict, List, Any
from dataclasses import asdict

from utils.testdata_types import TestResults

logger = logging.getLogger(__name__)


def load_input_data(file_path: str) -> List[str]:
    """
    Load input data from file.

    Supports JSON, CSV, and plain text formats.

    Args:
        file_path: Path to input file

    Returns:
        List of claims, where each line in the file is a separate claim
    """
    file_ext = os.path.splitext(file_path)[1].lower()

    try:
        if file_ext == ".txt":
            return load_text(file_path)
        else:
            logger.warning(
                f"Unsupported file extension: {file_ext}, trying to load as text"
            )
            return load_text(file_path)
    except Exception as e:
        logger.error(f"Error loading input data from {file_path}: {str(e)}")
        exit(1)


def save_output_data(data: TestResults, file_path: str) -> bool:
    """
    Save output data to file.

    Supports JSON, CSV, and YAML formats.
    Automatically converts Retrieval dataclass to dictionary.

    Args:
        data: Retrieval result to save
        file_path: Path to output file

    Returns:
        True if successful, False otherwise
    """
    file_ext = os.path.splitext(file_path)[1].lower()

    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Convert dataclass to dict
        data_dict = asdict(data)

        if file_ext == ".json":
            return save_json(data_dict, file_path)
        else:
            logger.warning(f"Unsupported file extension: {file_ext}, saving as JSON")
            return save_json(data_dict, file_path)
    except Exception as e:
        logger.error(f"Error saving output data to {file_path}: {str(e)}")
        return False


def load_text(file_path: str) -> List[str]:
    """
    Load data from text file.

    Returns a list of strings where each element is a line from the file.
    Empty lines are removed and each line is stripped of whitespace.
    """
    with open(file_path, "r") as f:
        # Read all lines, strip whitespace from each, and filter out empty lines
        lines = [line.strip() for line in f.readlines()]
        return [line for line in lines if line]


def save_json(data: List[Dict[str, Any]], file_path: str) -> bool:
    """Save data to JSON file."""
    with open(file_path, "w") as f:
        json.dump(data, f, indent=2)
    return True
