"""
Test Data Loader for YAML format.

This module provides functions for loading and parsing test data from YAML files
in the testdata.yaml format.
"""

import os
import yaml
import logging
from utils.testdata_types import TestData, TestCase

logger = logging.getLogger(__name__)


def load_testdata_yaml(file_path: str) -> TestData:
    """
    Load test data from YAML file.
    
    Args:
        file_path: Path to the YAML test data file
        
    Returns:
        TestData object containing all test sets and cases
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        yaml.YAMLError: If the YAML is invalid
        ValueError: If the YAML structure is invalid
    """
    if not os.path.exists(file_path):
        logger.error(f"Test data file not found: {file_path}")
        raise FileNotFoundError(f"Test data file not found: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            yaml_data = yaml.safe_load(f)
        
        logger.info(f"Loaded YAML test data from {file_path}")
        return _parse_testdata_yaml(yaml_data)
        
    except yaml.YAMLError as e:
        logger.error(f"Error parsing YAML file {file_path}: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error loading test data from {file_path}: {str(e)}")
        raise


def _parse_testdata_yaml(yaml_data: dict) -> TestData:
    """
    Parse YAML data into TestData object.
    
    Args:
        yaml_data: Parsed YAML data as dictionary
        
    Returns:
        TestData object
        
    Raises:
        ValueError: If the YAML structure is invalid
    """
    if not isinstance(yaml_data, dict):
        raise ValueError("YAML data must be a dictionary")
    
    # Validate required fields
    required_fields = ['test_cases']
    for field in required_fields:
        if field not in yaml_data:
            raise ValueError(f"Missing required field: {field}")
    
    test_cases = []
    for test_case in yaml_data['test_cases']:
        parsed_test_case = _parse_test_case(test_case)
        test_cases.append(parsed_test_case)
    
    return TestData(
        test_cases=test_cases
    )

def _parse_test_case(test_case_data: dict) -> TestCase:
    """
    Parse test case data into TestCase object.
    
    Args:
        test_case_data: Test case data as dictionary
        
    Returns:
        TestCase object
        
    Raises:
        ValueError: If the test case structure is invalid
    """
    if not isinstance(test_case_data, dict):
        raise ValueError("Test case data must be a dictionary")
    
    # Validate required fields
    required_fields = ['id', 'claim', 'expected_verification']
    for field in required_fields:
        if field not in test_case_data:
            raise ValueError(f"Missing required field in test case: {field}")
    
    # Validate expected_status
    valid_statuses = ['SUPPORTS', 'REFUTES', 'INSUFFICIENT']
    expected_verification = test_case_data['expected_verification']
    if expected_verification not in valid_statuses:
        raise ValueError(f"Invalid expected_status: {expected_verification}. Must be one of {valid_statuses}")
    
    return TestCase(
        id=test_case_data['id'],
        claim=test_case_data['claim'],
        expected_status=expected_verification,
    )
