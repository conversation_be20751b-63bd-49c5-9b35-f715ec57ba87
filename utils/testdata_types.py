"""
Type definitions for test data structure.

This module contains dataclasses for loading and working with YAML test data
in the format used by testdata.yaml.
"""

from dataclasses import dataclass, field
from typing import List

from pipeline.types.extractor_types import Extraction
from pipeline.types.retriever_types import Retrieval
from pipeline.types.verifier_types import VerificationStatus


@dataclass
class TestCase:
    """Represents a single test case."""
    
    id: str
    claim: str
    expected_status: VerificationStatus


@dataclass
class TestData:
    """Represents the complete test data structure."""
    test_cases: List[TestCase] = field(default_factory=list)


@dataclass
class TestResult:
    """Represents the result of running a single test case."""
    
    test_case_id: str
    claim: str
    expected_status: VerificationStatus
    actual_status: VerificationStatus
    explanation: str
    correct: bool
    extraction: Extraction
    retrieval: Retrieval


@dataclass
class StrategyTestResult:
    """Represents test results for a single strategy across all test sets."""
    
    strategy_name: str
    test_results: List[TestResult] = field(default_factory=list)


@dataclass
class TestResults:
    """Represents test results for multiple strategies."""
    
    strategy_results: List[StrategyTestResult] = field(default_factory=list)
