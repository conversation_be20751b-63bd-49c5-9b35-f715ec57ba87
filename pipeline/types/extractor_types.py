"""
Type definitions for the extractor module.

This module contains type definitions for the extractor module to ensure
type safety and avoid using Any types.
"""

from dataclasses import dataclass, field
from typing import List, Set


@dataclass
class FactTriple:
    """Structure of an extracted fact triple."""

    subject: str
    predicate: str
    object: str

    def get_entities(self) -> Set[str]:
        """Extract entities from this fact triple."""
        return {self.subject, self.object}


@dataclass
class Extraction:
    """Structure of the extraction result."""

    original_claim: str
    extraction_prompt_request: str
    extraction_prompt_result: str
    facts: List[FactTriple] = field(default_factory=list)
