"""
Prompt Service - Manages different prompt strategies for LLM interactions using LlamaIndex

This module provides a service for managing different prompt templates and strategies
for interacting with language models, including zero-shot, few-shot, and chain-of-thought,
using LlamaIndex's prompt templates.
"""

import os
import logging
from typing import Dict

from llama_index.core.prompts import RichPromptTemplate

from pipeline.services.prompt_types import ExtractorPromptData, VerifierPromptData, EntityFilteringPromptData
from config.config_types import PromptsConfig

logger = logging.getLogger(__name__)


class PromptService:
    """
    Service for managing different prompt templates and strategies using LlamaIndex.

    This class provides functionality to load and format prompts using
    different strategies like zero-shot, few-shot, and chain-of-thought,
    leveraging LlamaIndex's prompt template system.
    """
    prompts_config: PromptsConfig
    prompt_templates: Dict[str, RichPromptTemplate]

    def __init__(self, config: PromptsConfig):
        """
        Initialize the prompt service.

        Args:
            config: Configuration dictionary containing prompt settings
        """
        self.prompts_config = config
        self.prompt_templates = self._load_prompt_templates()
        logger.info("Prompt service initialized")

    def _load_prompt_templates(self) -> Dict[str, RichPromptTemplate]:
        """
        Load prompt templates from files and convert to LlamaIndex RichPromptTemplates.

        Returns:
            Dictionary of LlamaIndex RichPromptTemplates by strategy
        """
        templates: Dict[str, RichPromptTemplate] = {}

        for strategy, path in self.prompts_config.templates.items():
            if not path or not os.path.exists(path):
                logger.error(f"Prompt template file not found: {path}")
                exit(1)

            try:
                with open(path, "r", encoding="utf-8") as f:
                    template_text = f.read()

                templates[strategy] = RichPromptTemplate(template_str=template_text)
                logger.info(f"Loaded prompt template for strategy: {strategy}")
            except Exception as e:
                logger.error(f"Error loading prompt template for {strategy}: {str(e)}")

        return templates

    def get_verifier_prompt(self, strategy: str, input_data: VerifierPromptData) -> str:
        if strategy not in self.prompt_templates:
            logger.error(f"Strategy {strategy} not found")
            exit(1)

        template = self.prompt_templates[strategy]

        input_dict = {"claim": input_data.claim, "knowledge": input_data.knowledge}
        logger.info(f"Prompt {template.format(**input_dict)}")
        return template.format(**input_dict)


    def get_extractor_prompt(self, strategy: str, input_data: ExtractorPromptData) -> str:
        """
        Get a formatted prompt using the specified strategy.

        Args:
            strategy: Prompt strategy to use ('zero_shot', 'few_shot', 'cot', etc.)
            input_data: Input data to format the prompt with

        Returns:
            Formatted prompt string
        """
        # Check if we have the requested strategy
        if strategy not in self.prompt_templates:
            logger.error(f"Strategy {strategy} not found")
            exit(1)

        template = self.prompt_templates[strategy]

        input_dict = {"input_text": input_data.input_text}
        return template.format(**input_dict)