import logging
from typing import List

from config.config_types import AppConfig
from pipeline.extractor.extractor import FactExtractor
from pipeline.retriever.fuzzy_retriever import FuzzyRetriever
from pipeline.verifier.verifier import Verifier
from utils.testdata_types import TestCase, TestResult

logger = logging.getLogger(__name__)

class FactFencePipeline:
    config: AppConfig
    extractor: FactExtractor
    retriever: FuzzyRetriever
    verifier: Verifier

    def __init__(self, config: AppConfig):
        self.config = config
        self.extractor = FactExtractor(config)
        self.retriever = FuzzyRetriever(config)
        self.verifier = Verifier(config)
        logger.info("FactFence pipeline initialized")

    def verify_claims(self, claims: List[TestCase]) -> List[TestResult]:
        results: List[TestResult] = []

        for claim in claims:
            extraction_result = self.extractor.extract_facts(claim.claim)
            retrieval_results = self.retriever.retrieve(extraction_result)
            verification_results = self.verifier.verify(retrieval_results)
            results.append(TestResult(
                test_case_id=claim.id,
                claim=claim.claim,
                expected_status=claim.expected_status,
                actual_status=verification_results.status,
                explanation=verification_results.explanation,
                correct=(claim.expected_status == verification_results.status),
                extraction=extraction_result,
                retrieval=retrieval_results
            ))

        return results
