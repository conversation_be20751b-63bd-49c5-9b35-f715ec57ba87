version: "1.0"
description: "Test data for FactFence pipeline evaluation"

test_sets:
  - name: "basic_facts"
    description: "Basic factual claims for testing"
    test_cases:
      - id: "bf_001"
        claim: "Apple was founded in 1976."
        expected_status: "SUPPORTS"
        description: "Test basic company founding fact"

      - id: "bf_002"
        claim: "Microsoft was founded by <PERSON>."
        expected_status: "SUPPORTS"
        description: "Test company founder fact"

      - id: "bf_003"
        claim: "The Earth is flat."
        expected_status: "REFUTES"
        description: "Test obviously false claim"

      - id: "bf_004"
        claim: "Python is a programming language."
        expected_status: "SUPPORTS"
        description: "Test technology fact"

      - id: "bf_005"
        claim: "The capital of France is Berlin."
        expected_status: "REFUTES"
        description: "Test incorrect geography fact"

  - name: "historical_events"
    description: "Historical events and dates"
    test_cases:
      - id: "he_001"
        claim: "World War II ended in 1945."
        expected_status: "SUPPORTS"
        description: "Test historical date fact"

      - id: "he_002"
        claim: "The Berlin Wall fell in 1989."
        expected_status: "SUPPORTS"
        description: "Test historical event fact"

      - id: "he_003"
        claim: "The American Civil War started in 1850."
        expected_status: "REFUTES"
        description: "Test incorrect historical date"

      - id: "he_004"
        claim: "Napoleon was defeated at Waterloo."
        expected_status: "SUPPORTS"
        description: "Test historical battle fact"

  - name: "scientific_facts"
    description: "Scientific and technical facts"
    test_cases:
      - id: "sf_001"
        claim: "Water boils at 100 degrees Celsius at sea level."
        expected_status: "SUPPORTS"
        description: "Test basic physics fact"

      - id: "sf_002"
        claim: "The speed of light is approximately 300,000 km/s."
        expected_status: "SUPPORTS"
        description: "Test physics constant"

      - id: "sf_003"
        claim: "Humans have 46 chromosomes."
        expected_status: "SUPPORTS"
        description: "Test biology fact"

      - id: "sf_004"
        claim: "Gold has the chemical symbol Au."
        expected_status: "SUPPORTS"
        description: "Test chemistry fact"

      - id: "sf_005"
        claim: "The sun revolves around the Earth."
        expected_status: "REFUTES"
        description: "Test incorrect astronomy fact"

  - name: "ambiguous_claims"
    description: "Claims that might be ambiguous or require more context"
    test_cases:
      - id: "ac_001"
        claim: "Apple is red."
        expected_status: "INSUFFICIENT"
        description: "Test ambiguous claim (fruit vs company)"

      - id: "ac_002"
        claim: "The president signed the bill."
        expected_status: "INSUFFICIENT"
        description: "Test claim lacking specific context"

      - id: "ac_003"
        claim: "It rained yesterday."
        expected_status: "INSUFFICIENT"
        description: "Test location and time dependent claim"

      - id: "ac_004"
        claim: "The movie was good."
        expected_status: "INSUFFICIENT"
        description: "Test subjective opinion claim"

  - name: "complex_claims"
    description: "More complex claims with multiple facts"
    test_cases:
      - id: "cc_001"
        claim: "Apple was founded in 1976 by Steve Jobs and Steve Wozniak in California."
        expected_status: "SUPPORTS"
        description: "Test complex claim with multiple facts"

      - id: "cc_002"
        claim: "Einstein developed the theory of relativity and won the Nobel Prize in Physics in 1921."
        expected_status: "SUPPORTS"
        description: "Test complex scientific achievement claim"

      - id: "cc_003"
        claim: "The iPhone was released in 2007 and revolutionized the smartphone industry."
        expected_status: "SUPPORTS"
        description: "Test complex technology claim"

      - id: "cc_004"
        claim: "Shakespeare wrote Romeo and Juliet in the 16th century and it became his most famous play."
        expected_status: "SUPPORTS"
        description: "Test complex literary claim"