{"strategy_results": [{"strategy_name": "zero_shot", "test_results": [{"test_case_id": "test_001", "claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "Die bereitgestellten Wissensgraph-Tripel zeigen, dass das \"0E-Alterungsdossier\" sowohl \"wichtige Daten\" enthält ((0E-Alterungsdossier, enthält, wichtige Daten)) als auch \"Angaben zu Wartung\" ((0E-Alterungsdossier, enth<PERSON><PERSON>, <PERSON><PERSON><PERSON> zu Wartung)). Da sowohl Daten als auch Angaben im 0E-Alterungsdossier enthalten sind, wird die Behauptung unterstützt.", "correct": true, "extraction": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\nINPUT:\nDas Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line. Remove articles from the beginning of subjects and objects.", "extraction_prompt_result": "(Standarddokument \"0E-Alterungsdossier\", enthält, Daten)  \n(Standarddokument \"0E-Alterungsdossier\", enthält, Angaben)  \n(Standarddokument \"0E-Alterungsdossier\", hat <PERSON>ze<PERSON>ng, Das Standarddokument \"0E-Alterungsdossier\")", "facts": [{"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Das Standarddokument 0E-Alterungsdossier"}]}, "retrieval": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "retrievals": [{"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118476447", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1696", "name": "Beurteilung der Alterung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:366", "name": "Dokument", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519534", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "metadata": ["Incoming relation found for object entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:191", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.77}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152987475304513727", "type": "werden extrahiert aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:192", "name": "anlagenspezifische Betriebserfahrung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:211", "name": "<PERSON>", "similarity": 0.71}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152989674327769299", "type": "werden gesendet an"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:212", "name": "die Aufsichtsbehörde", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118474753", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2", "name": "Beurteilung der Alterungsüberwachung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152921504606846976", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "metadata": ["Incoming relation found for object entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:509", "name": "Angaben", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153066640141713917", "type": "sind enthalten"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:510", "name": "im Anhang A.3 der IEC 61513", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:577", "name": "Dosismeldung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153032555281252929", "type": "bein<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:578", "name": "Do<PERSON><PERSON><PERSON>", "similarity": 0.74}, "metadata": ["Incoming relation found for object entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155173304420532224", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:3", "name": "<PERSON><PERSON><PERSON>", "similarity": 0.56}, "metadata": ["Incoming relation found for object entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Das Standarddokument 0E-Alterungsdossier"}, "knowledge": []}]}}]}, {"strategy_name": "few_shot", "test_results": [{"test_case_id": "test_001", "claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "Die bereitgestellten Wissensgraph-Tripel zeigen, dass das \"0E-Alterungsdossier\" sowohl \"wichtige Daten\" enthält ((0E-Alterungsdossier, enthält, wichtige Daten)) als auch \"Angaben zu Wartung\" ((0E-Alterungsdossier, enth<PERSON><PERSON>, Ang<PERSON><PERSON> zu Wartung)). Damit ist belegt, dass das Standarddokument \"0E-Alterungsdossier\" sowohl Daten als auch Angaben enthält.", "correct": true, "extraction": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given text.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLES ###\n\nINPUT:\n\"Reaktorsicherheitskommission erstellt regelmäßig Berichte über die Anlagensicherheit.\"\n\nOUTPUT:\n(Reaktorsicherheitskommission, erstellt, Berichte über Anlagensicherheit)\n\n---\n\nINPUT:\n\"Primärkühlmittelkreislauf transportiert Wärme von Reaktorkern zu Wärmetauscher.\"\n\nOUTPUT:\n(Primärkühlmittelkreislauf, transportiert, Wärme)\n(Wärme, wird transportiert zu, Wärmetauscher)\n(Wärme, stammt aus, Reaktorkern)\n\n---\n\nINPUT:\n\"Jod-131 hat eine Halbwertszeit von acht Tagen und wird bei Kernspaltungsprozessen freigesetzt.\"\n\nOUTPUT:\n(Jod-131, hat, Halbwertszeit von acht Tagen)\n(Jod-131, wird freigesetzt bei, Kernspaltungsprozesse)\n\n---\n\n### Now process the following input:\n\nINPUT:\nDas Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.\n\nOUTPUT:\nList each atomic fact as a triple (subject, predicate, object) on a new line.", "extraction_prompt_result": "(Standarddokument 0E-Alterungsdossier, enthält, Daten)\n(Standarddokument 0E-Alterungsdossier, enthält, Angaben)\n(Standarddokument 0E-Alterungsdossier, hat <PERSON>ng, Standarddokument \"0E-Alterungsdossier\")", "facts": [{"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}]}, "retrieval": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "retrievals": [{"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118476447", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1696", "name": "Beurteilung der Alterung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:366", "name": "Dokument", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519534", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "metadata": ["Incoming relation found for object entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:191", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.77}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152987475304513727", "type": "werden extrahiert aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:192", "name": "anlagenspezifische Betriebserfahrung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:211", "name": "<PERSON>", "similarity": 0.71}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152989674327769299", "type": "werden gesendet an"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:212", "name": "die Aufsichtsbehörde", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118474753", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2", "name": "Beurteilung der Alterungsüberwachung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152921504606846976", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "metadata": ["Incoming relation found for object entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:509", "name": "Angaben", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153066640141713917", "type": "sind enthalten"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:510", "name": "im Anhang A.3 der IEC 61513", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:577", "name": "Dosismeldung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153032555281252929", "type": "bein<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:578", "name": "Do<PERSON><PERSON><PERSON>", "similarity": 0.74}, "metadata": ["Incoming relation found for object entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155173304420532224", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:3", "name": "<PERSON><PERSON><PERSON>", "similarity": 0.56}, "metadata": ["Incoming relation found for object entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}, "knowledge": []}]}}]}, {"strategy_name": "cot", "test_results": [{"test_case_id": "test_001", "claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "expected_status": "SUPPORTS", "actual_status": "SUPPORTS", "explanation": "Die bereitgestellten Wissensgraph-Tripel zeigen, dass das \"0E-Alterungsdossier\" sowohl \"wichtige Daten\" enthält ((0E-Alterungsdossier, enthält, wichtige Daten)) als auch \"Angaben zu Wartung\" ((0E-Alterungsdossier, enth<PERSON><PERSON>, <PERSON><PERSON><PERSON> zu Wartung)). Da \"wichtige Daten\" eine Untermenge von \"Daten\" sind und \"Angaben zu Wartung\" als \"Angaben\" betrachtet werden können, wird die Behauptung, dass das Standarddokument \"0E-Alterungsdossier\" sowohl Daten als auch Angaben enthält, durch das Wissen unterstützt.", "correct": true, "extraction": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "extraction_prompt_request": "You are a precise fact extraction system. Your task is to extract atomic facts from a given German sentence and output them as clean (subject, predicate, object) triples.\n\nINSTRUCTIONS:\n- Break down the input text into individual, atomic facts\n- Each fact should be a simple, standalone statement\n- Preserve the original meaning and context\n- Do not add any information not present in the original text\n- Do not make assumptions or inferences beyond what is explicitly stated\n- Each fact should be a complete sentence\n- Avoid redundancy and repetition\n- Extract only factual statements, not opinions or subjective claims\n- Return each fact as a triple in the format (subject, predicate, object)\n- Remove articles (\"the\", \"a\", \"an\", \"das\", \"die\", \"der\", etc.) from the beginning of subjects and objects\n- Keep the core entity names without articles to facilitate entity matching\n- When extracting subjects and objects, prefer the core name or identifier of an entity, and avoid including descriptive prefixes or qualifiers\n- If context is important or clarity may be lost, you may optionally add an additional alias triple in the form: (entity, hat Bezeichnung, full_phrase), where full_phrase is the original compound form from the text.\n- Ensure entity names are clean and directly matchable with knowledge graph entries\n- Always keep the language of the triples in German. Do not translate or change the language of the input or output; the triples must remain in German.\n\n### EXAMPLE:\n\nINPUT:\n\"Das Notstromaggregat startet automatisch bei Stromausfall und versorgt sicherheitsrelevante Systeme.\"\n\nREASONING:\n- Identify main subject: Notstromaggregat\n- It performs two actions:\n  - startet automatisch bei Stromausfall\n  - versorgt sicherheitsrelevante Systeme\n- Extract each as a separate triple\n- Remove \"Das\" from subject\n- Result: two clean factual triples\n\nOUTPUT:\n(Notstromaggregat, startet bei, Stromausfall)\n(Notstromaggregat, versorgt, sicherheitsrelevante Systeme)\n\n---\n\nINPUT:\nDas Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.\n\nREASONING:\n[Let the model walk through the reasoning here.]\n\nOUTPUT:\n[List the final clean triples here.]", "extraction_prompt_result": "REASONING:\n- Identify main subject: Standarddokument \"0E-Alterungsdossier\"\n- Remove article \"Das\" from subject\n- The document \"enthält sowohl Daten als auch Angaben\" (contains both data and information)\n- Two objects: Daten (data) and <PERSON><PERSON><PERSON> (information)\n- The document has a specific name: \"0E-Alterungsdossier\"\n- The full phrase is \"Standarddokument '0E-Alterungsdossier'\", so an alias triple can be added for clarity\n\nOUTPUT:\n(Standarddokument 0E-Alterungsdossier, enthält, Daten)\n(Standarddokument 0E-Alterungsdossier, enthält, Angaben)\n(Standarddokument 0E-Alterungsdossier, hat Bezeichnung, Standarddokument \"0E-Alterungsdossier\")", "facts": [{"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}]}, "retrieval": {"original_claim": "Das Standarddokument \"0E-Alterungsdossier\" enthält sowohl Daten als auch Angaben.", "retrievals": [{"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Daten"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118476447", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1696", "name": "Beurteilung der Alterung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:366", "name": "Dokument", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152940196304519534", "type": "hat"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1695", "name": "Daten", "similarity": 1.0}, "metadata": ["Incoming relation found for object entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:191", "name": "<PERSON><PERSON><PERSON><PERSON>", "similarity": 0.77}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152987475304513727", "type": "werden extrahiert aus"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:192", "name": "anlagenspezifische Betriebserfahrung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:211", "name": "<PERSON>", "similarity": 0.71}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152989674327769299", "type": "werden gesendet an"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:212", "name": "die Aufsichtsbehörde", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152922604118474753", "type": "werden benutzt für"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:2", "name": "Beurteilung der Alterungsüberwachung", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1152921504606846976", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:1", "name": "wich<PERSON><PERSON> Daten", "similarity": 0.53}, "metadata": ["Incoming relation found for object entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "<PERSON>th<PERSON><PERSON>", "object": "Angaben"}, "knowledge": [{"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:509", "name": "Angaben", "similarity": 1.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153066640141713917", "type": "sind enthalten"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:510", "name": "im Anhang A.3 der IEC 61513", "similarity": 0.0}, "metadata": ["Outgoing relation found for subject entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:577", "name": "Dosismeldung", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1153032555281252929", "type": "bein<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:578", "name": "Do<PERSON><PERSON><PERSON>", "similarity": 0.74}, "metadata": ["Incoming relation found for object entity"]}, {"subject": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:0", "name": "0E-Alterungsdossier", "similarity": 0.0}, "predicate": {"id": "5:0e581861-b896-4e7e-ac6c-727cadc45043:1155173304420532224", "type": "<PERSON>th<PERSON><PERSON>"}, "object": {"id": "4:0e581861-b896-4e7e-ac6c-727cadc45043:3", "name": "<PERSON><PERSON><PERSON>", "similarity": 0.56}, "metadata": ["Incoming relation found for object entity"]}]}, {"fact": {"subject": "Standarddokument 0E-Alterungsdossier", "predicate": "hat Bezeichnung", "object": "Standarddokument 0E-Alterungsdossier"}, "knowledge": []}]}}]}]}