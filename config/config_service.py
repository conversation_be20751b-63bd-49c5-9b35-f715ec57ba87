"""
Configuration service for FactFence.

This module handles loading and managing configuration from both
command line arguments and configuration files.
"""

import yaml
import logging
from argparse import ArgumentParser, Namespace
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from config.config_types import (
    AppConfig,
    OutputConfig,
    KnowledgeGraphConfig,
    FactExtractionConfig,
    ModelsConfig,
    ModelConfig,
    PromptsConfig,
    LoggingConfig,
    InputConfig,
    FuzzyRetrievalConfig,
    KnowledgeGraphConnectionConfig,
    VerifierConfig, PromptStrategy, VALID_STRATEGIES,
)

logger = logging.getLogger(__name__)


class ConfigurationService:
    """Service for managing application configuration."""

    def __init__(self):
        """Initialize the configuration service."""
        self.config: Optional[AppConfig] = None

    def setup(self) -> AppConfig:
        parser = self.setup_argument_parser()
        args = parser.parse_args()
        load_dotenv()
        return self.load_configuration(args)

    def get_strategies_list(self) -> List[PromptStrategy]:
        strategy = self.config.fact_extraction.strategies
        if isinstance(strategy, list):
            if all(isinstance(s, str) and s in VALID_STRATEGIES for s in strategy):
                return strategy
            else:
                raise ValueError(f"Invalid strategy list: {strategy}")
        else:
            raise ValueError(f"Invalid strategy type: {type(strategy)}")

    def load_configuration(self, args: Namespace) -> AppConfig:
        """
        Load and merge configuration from all sources.

        Args:
            args: Parsed command line arguments

        Returns:
            Complete application configuration
        """
        config_dict = self._load_config_file(args.config)
        config_dict = self._merge_cli_args(config_dict, args)
        self.config = self._create_config_objects(config_dict)

        return self.config

    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        try:
            with open(config_path, "r") as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error loading configuration file: {str(e)}")
            return {}

    def _merge_cli_args(
        self, config: Dict[str, Any], args: Namespace
    ) -> Dict[str, Any]:
        if args.output:
            config["output"]["directory"] = args.output

        if args.inputFile:
            config["input"]["claims_file"] = args.inputFile

        return config

    def _create_config_objects(self, config: Dict[str, Any]) -> AppConfig:
        models_config = config["fact_extraction"]["models"]
        available_models = {
            name: ModelConfig(**model_config)
            for name, model_config in models_config["available"].items()
        }

        models = ModelsConfig(
            selected=models_config["selected"], available=available_models
        )

        connection_config = KnowledgeGraphConnectionConfig(
            uri=config["knowledge_graph"]["connection"]["uri"],
            username=config["knowledge_graph"]["connection"]["username"],
            password_env=config["knowledge_graph"]["connection"]["password_env"],
            database=config["knowledge_graph"]["connection"]["database"],
        )

        fuzzy_retrieval_config = FuzzyRetrievalConfig(
            similarity_threshold=config["fuzzy_retrieval"]["similarity_threshold"],
            skip_second_stage=config["fuzzy_retrieval"]["skip_second_stage"],
            max_entity_search_retrievals=config["fuzzy_retrieval"]["max_entity_search_retrievals"],
            max_entity_matches=config["fuzzy_retrieval"]["max_entity_matches"],
            max_relation_search_retrievals=config["fuzzy_retrieval"]["max_relation_search_retrievals"],
        )

        return AppConfig(
            input=InputConfig(
                claims_file=config["input"]["claims_file"],
                testdata_file=config["input"]["testdata_file"]
            ),
            output=OutputConfig(directory=config["output"]["directory"]),
            knowledge_graph=KnowledgeGraphConfig(
                type=config["knowledge_graph"]["type"],
                connection=connection_config,
                import_config=config["knowledge_graph"]["import"],
            ),
            fact_extraction=FactExtractionConfig(
                strategies=config["fact_extraction"]["strategies"],
                strategy=None,
                models=models,
                prompts=PromptsConfig(
                    templates=config["fact_extraction"]["prompts"]["templates"],
                ),
            ),
            fuzzy_retrieval=fuzzy_retrieval_config,
            verifier=VerifierConfig(
                strategy=config["verifier"]["strategy"],
                models=models,
                prompts=PromptsConfig(
                    templates=config["verifier"]["prompts"]["templates"],
                ),
            ),
            logging=LoggingConfig(
                level=config["logging"]["level"],
                format=config["logging"]["format"],
                log_dir=config["logging"]["log_dir"],
            ),
        )

    @staticmethod
    def setup_argument_parser() -> ArgumentParser:
        """Create and configure argument parser."""
        parser = ArgumentParser(description="Fact Verification Framework")
        parser.add_argument(
            "--config",
            default="config/settings.yaml",
            help="Path to configuration file",
        )
        parser.add_argument(
            "--inputFile", default=None, help="Path to input claims file"
        )
        parser.add_argument("--output", default=None, help="Path to output directory")
        return parser
